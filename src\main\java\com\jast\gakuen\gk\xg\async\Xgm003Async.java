/*
 * Xgm003Async.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.async;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import com.jast.gakuen.core.common.SessionInfo;
import com.jast.gakuen.core.common.UserInfo;
import com.jast.gakuen.core.common.annotation.RxAsyncErrorCheck;
import com.jast.gakuen.core.common.annotation.RxAsyncExecute;
import com.jast.gakuen.core.common.annotation.RxAsyncInit;
import com.jast.gakuen.core.common.annotation.RxAsyncNumberCount;
import com.jast.gakuen.core.common.async.BaseCollectiveOutputAsync;
import com.jast.gakuen.core.common.constant.FileTypeConst;
import com.jast.gakuen.core.common.constant.code.PrdKbn;
import com.jast.gakuen.core.common.database.DbSession;
import com.jast.gakuen.core.common.dto.FileDTO;
import com.jast.gakuen.core.common.util.Message;
import com.jast.gakuen.core.common.util.RxLogger;
import com.jast.gakuen.core.common.async.CollectiveCsvWriter;
import com.jast.gakuen.core.common.async.ICollectiveWriter;
import com.jast.gakuen.core.gk.util.UtilFormWriter;
import com.jast.gakuen.gk.xg.dto.Xgm003AsyncDTO;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO02;
import com.jast.gakuen.gk.xg.service.IXgm003Service;

/**
 * 学生納付金通知書出力（CSV+PDF一括ダウンロード）非同期処理クラス
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
public class Xgm003Async extends BaseCollectiveOutputAsync {

	/**
	 * プロダクトコード
	 */
	public static final String PRD_CD = "Xg";

	/**
	 * 非同期処理ID
	 */
	public static final String ASYNC_EXEC_ID = "Xgm003Async";

	/**
	 * 処理名：CSV+PDF一括ダウンロード
	 */
	public static final String EXEC_PACKAGE_OUTPUT = "EXEC_PACKAGE_OUTPUT";

	/**
	 * ログ出力
	 */
	private static final RxLogger logger = new RxLogger(Xgm003Async.class);

	/**
	 * ログインユーザ情報
	 */
	private UserInfo userInfo;

	/**
	 * 学生納付金通知書サービス
	 */
	@Inject
	private IXgm003Service xgm003Service;

	/**
	 * コンストラクタ
	 *
	 * @throws Exception 例外
	 */
	public Xgm003Async() throws Exception {
		super();
	}

	/**
	 * 初期処理
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 */
	@RxAsyncInit(EXEC_PACKAGE_OUTPUT)
	public void init(final DbSession dbs, final Xgm003AsyncDTO condition) {
		// ログインユーザ情報を取得
		SessionInfo sessionInfo = SessionInfo.getSessionInfo();
		this.userInfo = sessionInfo.getLoginUser();
	}

	/**
	 * エラーチェック
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return エラーチェック結果
	 * @throws Exception 例外
	 */
	@RxAsyncErrorCheck(EXEC_PACKAGE_OUTPUT)
	public List<Message> errorCheck(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		List<Message> messageList = new ArrayList<>();
		
		// 基本的なバリデーション
		if (condition.getConditionHeader() == null) {
			messageList.add(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 36, "出力条件"));
		}

		return messageList;
	}

	/**
	 * 処理件数取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 処理件数
	 * @throws Exception 例外
	 */
	@RxAsyncNumberCount(EXEC_PACKAGE_OUTPUT)
	public int getCount(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		// 処理件数は固定で2（CSV + PDF）
		return 2;
	}

	/**
	 * 業務処理：CSV+PDF一括ダウンロード
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	@RxAsyncExecute(EXEC_PACKAGE_OUTPUT)
	public void executePackageOutput(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {

		logger.info("CSV+PDF一括ダウンロード処理開始");
		
		// 圧縮対象ファイルのリスト
		List<FileDTO> zipFileDtoList = new ArrayList<>();
		
		try {
			// 1. CSV ファイル生成
			FileDTO csvFileDto = generateCsvFile(dbs, condition);
			if (csvFileDto != null) {
				zipFileDtoList.add(csvFileDto);
				addNormalCount(1);
				addCount(1);
			}
			
			// 2. PDF ファイル生成
			FileDTO pdfFileDto = generatePdfFile(dbs, condition);
			if (pdfFileDto != null) {
				zipFileDtoList.add(pdfFileDto);
				addNormalCount(1);
				addCount(1);
			}
			
			// 3. ZIP ファイル生成
			if (!zipFileDtoList.isEmpty()) {
				FileDTO zipFileDTO = getOutputFileDTO(dbs, condition.getPrdCd(), condition.getZipFileId(), FileTypeConst.ZIP);
				// ZIPファイルを生成し、圧縮対象ファイルを登録する
				compressOutputFile(zipFileDTO, zipFileDtoList, true);
			}
			
		} catch (Exception e) {
			logger.error(Xgm003Async.class, e);
			addErrorCount(1);
			throw e;
		}

		logger.info("CSV+PDF一括ダウンロード処理終了");
	}

	/**
	 * CSV ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return CSV ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generateCsvFile(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		logger.info("CSV ファイル生成開始");

		// CSV ファイルのFileDTOを取得
		FileDTO csvFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), condition.getCsvFileId(), FileTypeConst.CSV);

		// 三菱UFJ Factor向けのCSVフォーマットでデータを出力
		try (ICollectiveWriter csvWriter = new CollectiveCsvWriter(csvFileDto)) {

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// データ行を出力（三菱UFJ Factor指定フォーマット）
			for (Xgm003DTO02 data : dataList) {
				writeCsvDataLine(csvWriter, data, condition);
				addNormalCount(1);
				addCount(1);
			}

			// CSVファイルをクローズ
			csvWriter.close();

			logger.info("CSV ファイル生成完了：" + dataList.size() + "件");
		}

		logger.info("CSV ファイル生成終了");
		return csvFileDto;
	}

	/**
	 * PDF ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return PDF ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generatePdfFile(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		logger.info("PDF ファイル生成開始");
		
		// PDF ファイルのFileDTOを取得
		FileDTO pdfFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), condition.getPdfFileId(), FileTypeConst.PDF);
		
		// SVF を使用してPDF生成
		UtilFormWriter pdfWriter = new UtilFormWriter();

		try {
			// PDF ファイルを開く
			pdfWriter.open(UtilFormWriter.DocFileType.PDF, pdfFileDto.getAbsolutePath());

			// SVF テンプレート（Xgm003RPT01.xml）を使用してPDF生成
			pdfWriter.setForm(com.jast.gakuen.core.common.constant.code.PrdKbn.GH, "Xgm003RPT01");

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// 各納付金データに対してPDFページを生成
			for (Xgm003DTO02 data : dataList) {
				// 基本情報を設定
				pdfWriter.print("年度", String.valueOf(data.getNendo()));
				pdfWriter.print("納付金コード", data.getPayCd());
				pdfWriter.print("納付金名称", data.getPayName());
				pdfWriter.print("分納区分", data.getBunnoKbnName());
				pdfWriter.print("分割番号", String.valueOf(data.getBunkatsuNo()));

				// 納入期限を設定
				if (data.getPayLimitDate() != null) {
					pdfWriter.print("納入期限", data.getPayLimitDate().toString());
				}

				// 発行日を設定
				if (condition.getCondition() != null && condition.getCondition().getHattyuDate() != null) {
					pdfWriter.print("発行日", condition.getCondition().getHattyuDate().toString());
				}

				// 通信欄を設定
				if (condition.getCondition() != null && condition.getCondition().getTsuukyak() != null) {
					pdfWriter.print("通信欄", condition.getCondition().getTsuukyak());
				}

				// 次のページに移動（最後のデータでない場合）
				pdfWriter.next();

				addNormalCount(1);
				addCount(1);
			}

			logger.info("PDF ファイル生成完了：" + dataList.size() + "件");

		} finally {
			pdfWriter.close();
		}
		
		logger.info("PDF ファイル生成終了");
		return pdfFileDto;
	}

	/**
	 * 納付金通知書データを取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 納付金通知書データリスト
	 * @throws Exception 例外
	 */
	private List<Xgm003DTO02> getNotificationData(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		// 検索条件を作成
		Xgm003ConditionDTO02 searchCondition = new Xgm003ConditionDTO02();

		// 基本的な検索条件を設定
		if (condition.getConditionHeader() != null) {
			// ヘッダ部の基本条件を設定
			// 実際の条件設定は画面の条件に合わせて調整が必要
		}

		// サービスを使用してデータを取得
		return xgm003Service.getPayHList(searchCondition);
	}

	/**
	 * CSV データ行を出力
	 *
	 * @param csvWriter CSV ライター
	 * @param data 納付金データ
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	private void writeCsvDataLine(final ICollectiveWriter csvWriter, final Xgm003DTO02 data, final Xgm003AsyncDTO condition) throws Exception {
		// 三菱UFJ Factor向けCSVフォーマットで出力
		// フォーマット：年度,納付金コード,パターンコード,分納区分コード,分割NO,納付金名称,納入期限
		List<String> csvLine = new ArrayList<>();
		csvLine.add(String.valueOf(data.getNendo()));                    // 年度
		csvLine.add(data.getPayCd());                                   // 納付金コード
		csvLine.add(data.getPatternCd());                               // パターンコード
		csvLine.add(String.valueOf(data.getBunnoKbnCd()));              // 分納区分コード
		csvLine.add(String.valueOf(data.getBunkatsuNo()));              // 分割NO
		csvLine.add(data.getPayName());                                 // 納付金名称
		csvLine.add(data.getPayLimitDate() != null ?
			data.getPayLimitDate().toString() : "");                   // 納入期限

		// CSV行を出力
		csvWriter.writeLineData(csvLine, false);
	}

}
