/*
 * Xgm00301T03Bean.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.bean;

import java.util.ArrayList;
import java.util.List;

import javax.enterprise.context.SessionScoped;
import javax.inject.Inject;
import javax.inject.Named;

import com.jast.gakuen.core.common.BaseSubBean;
import com.jast.gakuen.core.common.SystemInfo;
import com.jast.gakuen.core.common.dto.OptionDTO;
import com.jast.gakuen.core.common.service.IOptionService;
import com.jast.gakuen.core.common.service.IOrdService;
import com.jast.gakuen.core.common.util.Transporter;
import com.jast.gakuen.core.common.util.UtilDialog;
import com.jast.gakuen.core.common.util.UtilFaces;
import com.jast.gakuen.core.common.util.UtilStr;
import com.jast.gakuen.core.gk.annotation.GkBackingBean;
import com.jast.gakuen.core.gk.annotation.GkWindowOpen;
import com.jast.gakuen.gk.gh.dto.Ghb201DTO02;
import com.jast.gakuen.gk.gh.dto.Ghb201DTO03;
import com.jast.gakuen.gk.gh.dto.Ghd008CollectiveOutputDTO02;
import com.jast.gakuen.gk.gh.dto.Ghd008CollectiveOutputDTO03;
import com.jast.gakuen.gk.gh.dto.Ghd008ConditionDTO01;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO05;
import com.jast.gakuen.gk.pk.service.IPkzPrinterService;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO04;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;
import com.jast.gakuen.gk.xg.service.IXgm003Service;

import lombok.Getter;
import lombok.Setter;

/**
 * 学生納付金通知書(学生指定)
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Named
@SessionScoped
public class Xgm00301T03Bean extends BaseSubBean {

	/**
	 * 学生納付金通知書ービス
	 */
	@Inject
	protected IXgm003Service xgm003Service;

	/**
	 * プリンタサービス
	 */
	@Inject
	protected IPkzPrinterService printerService;

	/**
	 * 学生納付金通知書出力
	 */
	@Inject
	@Getter
	protected Xgm00301Bean xgm00301Bean;

	/**
	 * 入出力項目並び順サービス
	 */
	@Inject
	protected IOrdService ordService;

	/**
	 * オプションサービス
	 */
	@Inject
	protected IOptionService optionService;

	/**
	 * トランスポーター
	 */
	protected final Transporter transporter = new Transporter();

	/**
	 * 学生指定 検索条件DTO
	 */
	@Getter
	protected final Xgm003ConditionDTO04 condition = new Xgm003ConditionDTO04();

	/**
	 * 学生納付金通知書出力(データ)DTO(非同期処理)
	 */
	@Getter
	protected final Ghd008CollectiveOutputDTO02 collectiveOutputData = new Ghd008CollectiveOutputDTO02();

	/**
	 * 学生納付金通知書出力(帳票)DTO(非同期処理)
	 */
	@Getter
	protected final Ghd008CollectiveOutputDTO03 collectiveOutputReport = new Ghd008CollectiveOutputDTO03();

	/**
	 * 納付金リスト
	 */
	@Getter
	protected List<Xgm003DTO02> payList = new ArrayList<>();

	/**
	 * 選択された納付金リスト
	 */
	@Getter
	@Setter
	protected List<Xgm003DTO02> selectedPayList = new ArrayList<>();

	/**
	 * 学生氏名
	 */
	@Getter
	protected String gakseiName;

	/**
	 * 検索済みフラグ
	 */
	@Getter
	protected boolean searched = false;

	/**
	 * 初期表示処理
	 *
	 * @param conditionOutput 出力内容指定DTO
	 * @throws Exception 例外
	 */
	public void doInit(final Ghd008ConditionDTO01 conditionOutput) throws Exception {

		// ------------------------------
		// 初期値設定
		// ------------------------------
		// 納付金リスト
		this.getPayList().clear();
		this.getPayList().addAll(selectedPayList);

		// ------------------------------
		// オプション情報を取得
		// ------------------------------
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition);
		this.optionService.get(option);

	}

	/**
	 * 「学生検索」ボタン押下
	 *
	 * @return 子画面ＩＤ
	 */
	@GkBackingBean
	@GkWindowOpen(other = "{\"openId\":\"Xgm00301T03:openChildWindow\"}")
	public String doOpenSearchGakseki() {

		// ・ 検索モード ： 0（単一検索モード）
		Ghb201DTO02 param = new Ghb201DTO02();
		param.setSearchMode("0");

		UtilDialog.setDialogParameters(param);
		return "Ghb20101";
	}

	/**
	 * 学生検索画面クローズ
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doReceiveGaksekiCd() throws Exception {
		List<Ghb201DTO03> rtnList = UtilDialog.getDialogParameters();
		if (!rtnList.isEmpty()) {
			this.condition.setGaksekiCd(rtnList.get(0).getGaksekiCd());
			this.doGetGakName();
		}
	}

	/**
	 * 学生氏名の取得
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doGetGakName() throws Exception {
		this.gakseiName = null;
		if (UtilStr.isEmpty(this.condition.getGaksekiCd())) {
			return;
		}
		Ghd008DTO05 paramDto = new Ghd008DTO05();
		paramDto.setSkijunDate(SystemInfo.getSystemDate());
		paramDto.setGaksekiCd(this.condition.getGaksekiCd());
		Ghd008DTO05 rtnDto = this.xgm003Service.getGakseiName(paramDto);
		if (rtnDto != null) {
			this.condition.setKanriNo(rtnDto.getKanriNo());
			this.gakseiName = rtnDto.getGakseiName();
		}
	}

	/**
	 * 検索処理
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doSearch() throws Exception {

		// 納付金リストの初期化
		this.payList = new ArrayList<>();
		this.selectedPayList = new ArrayList<>();

		List<Xgm003DTO02> payWList = this.xgm003Service.getPayWList(this.condition);

		this.payList.addAll(payWList);
		this.searched = true;
	}

	/**
	 * クリア処理
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doClear() throws Exception {
		// 納付金リストの初期化
		this.payList = new ArrayList<>();
		this.selectedPayList = new ArrayList<>();
	}

	/**
	 * オプション情報に保存
	 *
	 * @throws Exception 例外
	 */
	protected void saveOption() throws Exception {
		this.xgm00301Bean.saveOption();

		// オプション情報を保存
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition);
		this.optionService.set(option);
	}

	/**
	 * 延納完了の納付金選択判定
	 * 
	 * @return true/false
	 */
	protected boolean isExistEnnoKanryo() {
		for (Xgm003DTO02 dto : this.selectedPayList) {
			if (dto.isEnnoKanryoFlg()) {
				return true;
			}
		}
		return false;
	}
}
