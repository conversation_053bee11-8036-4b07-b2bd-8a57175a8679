/*
 * Xgm003AsyncDTO.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import com.jast.gakuen.core.common.dto.BaseAsyncRequestDTO;
import com.jast.gakuen.core.common.constant.FileTypeConst;

import lombok.Getter;
import lombok.Setter;

/**
 * 学生納付金通知書出力（CSV+PDF一括ダウンロード）非同期処理DTO
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003AsyncDTO extends BaseAsyncRequestDTO {

	/**
	 * プロダクトコード
	 */
	public static final String PRD_CD = "Xg";

	/**
	 * 非同期処理ID
	 */
	public static final String ASYNC_EXEC_ID = "Xgm003Async";

	/**
	 * 処理コード
	 */
	public static final String EXEC_CD = "EXEC_PACKAGE_OUTPUT";

	/**
	 * コンストラクタ
	 */
	public Xgm003AsyncDTO() {
		// 非同期起動パラメータ
		super.setPrdCd(PRD_CD);
		super.setAsyncExecId(ASYNC_EXEC_ID);
		super.setExecCd(EXEC_CD);
		super.setAsyncExecClass(com.jast.gakuen.gk.xg.async.Xgm003Async.class);
	}

	/**
	 * 出力ファイル形式
	 */
	private FileTypeConst fileType = FileTypeConst.PRINT;

	/**
	 * ヘッダ部条件
	 */
	private Xgm003ConditionDTO02 conditionHeader;

	/**
	 * メイン条件
	 */
	private Xgm003ConditionDTO01 condition;

	/**
	 * CSVファイルID
	 */
	private String csvFileId = "XGM003_CSV01";

	/**
	 * PDFファイルID
	 */
	private String pdfFileId = "XGM003_PDF01";

	/**
	 * ZIPファイルID
	 */
	private String zipFileId = "XGM003_ZIP01";

}
